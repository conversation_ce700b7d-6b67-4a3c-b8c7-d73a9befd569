#!/usr/bin/env python3
"""
Comprehensive test to verify the enhanced legend functionality
meets all specified requirements.
"""

import sys
import pandas as pd
import re
from PyQt5.QtWidgets import QApplication
from mt4_analyzer import App

def test_legend_format_compliance():
    """Test that the legend format exactly matches the specification."""
    print("Testing Legend Format Compliance")
    print("=" * 50)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create the analyzer
        analyzer = App()
        analyzer.file_path = 'Statement.htm'
        
        # Parse the data
        df = analyzer.parse_statement_html(analyzer.file_path)
        
        # Data processing
        for col in ['Profit', 'Commission', 'Swap', 'Size']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df['Open Time'] = pd.to_datetime(df['Open Time'], errors='coerce')
        df['Close Time'] = pd.to_datetime(df['Close Time'], errors='coerce')
        df['Net Profit'] = df['Profit'] + df['Commission'] + df['Swap']
        
        print(f"📊 Testing with {len(df)} trades from Statement.htm")
        
        # Test both identification methods
        test_cases = [
            ("Comment", lambda x: ' '.join(str(x).split()[:1]) if pd.notna(x) else "No Comment", df['Comment']),
            ("Magic Number", lambda x: f"Magic #{str(x)}" if pd.notna(x) and str(x).strip() else "No Magic Number", df['Magic Number'])
        ]
        
        all_tests_passed = True
        
        for method_name, id_func, source_column in test_cases:
            print(f"\n🔍 Testing {method_name}-based identification:")
            print("-" * 40)
            
            df['EA_Name'] = source_column.apply(id_func)
            grouped_by_ea = df.groupby('EA_Name')
            
            for ea_name, ea_df in grouped_by_ea:
                if ea_df.empty:
                    continue
                
                # Generate the legend text exactly as the app does
                start_date = ea_df['Open Time'].min().strftime('%Y-%m-%d')
                end_date = ea_df['Close Time'].max().strftime('%Y-%m-%d')
                currency_pairs = ea_df['Item'].str.upper().unique()
                currency_pairs = sorted([pair for pair in currency_pairs if pd.notna(pair) and pair.strip()])
                pairs_text = ', '.join(currency_pairs) if currency_pairs else 'N/A'
                legend_text = f"EA: {ea_name} | Pairs: {pairs_text} | Start: {start_date} | End: {end_date} | Trades: {len(ea_df)}"
                
                print(f"\n✅ Legend: {legend_text}")
                
                # Test 1: Format compliance
                expected_pattern = r"^EA: .+ \| Pairs: .+ \| Start: \d{4}-\d{2}-\d{2} \| End: \d{4}-\d{2}-\d{2} \| Trades: \d+$"
                if re.match(expected_pattern, legend_text):
                    print("   ✓ Format matches specification pattern")
                else:
                    print("   ✗ Format does NOT match specification pattern")
                    all_tests_passed = False
                
                # Test 2: Component extraction
                components = legend_text.split(' | ')
                if len(components) == 5:
                    print("   ✓ Has exactly 5 components")
                    
                    # Test each component
                    if components[0].startswith('EA: '):
                        print(f"   ✓ EA component: '{components[0]}'")
                    else:
                        print(f"   ✗ EA component malformed: '{components[0]}'")
                        all_tests_passed = False
                    
                    if components[1].startswith('Pairs: '):
                        pairs_part = components[1][7:]  # Remove "Pairs: "
                        if ',' in pairs_part:
                            pairs_list = [p.strip() for p in pairs_part.split(',')]
                            if pairs_list == sorted(pairs_list):
                                print(f"   ✓ Pairs component (sorted): '{components[1]}'")
                            else:
                                print(f"   ✗ Pairs not sorted: '{components[1]}'")
                                all_tests_passed = False
                        else:
                            print(f"   ✓ Pairs component (single): '{components[1]}'")
                    else:
                        print(f"   ✗ Pairs component malformed: '{components[1]}'")
                        all_tests_passed = False
                    
                    if components[2].startswith('Start: ') and re.match(r'Start: \d{4}-\d{2}-\d{2}', components[2]):
                        print(f"   ✓ Start date component: '{components[2]}'")
                    else:
                        print(f"   ✗ Start date component malformed: '{components[2]}'")
                        all_tests_passed = False
                    
                    if components[3].startswith('End: ') and re.match(r'End: \d{4}-\d{2}-\d{2}', components[3]):
                        print(f"   ✓ End date component: '{components[3]}'")
                    else:
                        print(f"   ✗ End date component malformed: '{components[3]}'")
                        all_tests_passed = False
                    
                    if components[4].startswith('Trades: ') and components[4][8:].isdigit():
                        trade_count = int(components[4][8:])
                        if trade_count == len(ea_df):
                            print(f"   ✓ Trade count component: '{components[4]}'")
                        else:
                            print(f"   ✗ Trade count mismatch: '{components[4]}' vs actual {len(ea_df)}")
                            all_tests_passed = False
                    else:
                        print(f"   ✗ Trade count component malformed: '{components[4]}'")
                        all_tests_passed = False
                        
                else:
                    print(f"   ✗ Has {len(components)} components, expected 5")
                    all_tests_passed = False
                
                # Test 3: Currency pair extraction accuracy
                actual_pairs = set(ea_df['Item'].str.upper().dropna().unique())
                legend_pairs = set(pairs_text.split(', ')) if pairs_text != 'N/A' else set()
                
                if actual_pairs == legend_pairs:
                    print(f"   ✓ Currency pairs accurate: {actual_pairs}")
                else:
                    print(f"   ✗ Currency pairs mismatch: actual {actual_pairs} vs legend {legend_pairs}")
                    all_tests_passed = False
        
        print(f"\n{'='*50}")
        print("COMPLIANCE TEST SUMMARY")
        print("=" * 50)
        
        if all_tests_passed:
            print("🎉 ALL TESTS PASSED!")
            print("✅ Legend format fully complies with specification:")
            print("   • Format: 'EA: [EA_Name] | Pairs: [PAIR1, PAIR2, PAIR3] | Start: [DATE] | End: [DATE] | Trades: [COUNT]'")
            print("   • EA names displayed correctly")
            print("   • Currency pairs extracted from 'Item' column")
            print("   • Currency pairs sorted alphabetically")
            print("   • Date format: YYYY-MM-DD")
            print("   • Trade counts accurate")
            print("   • Component separation with ' | '")
        else:
            print("❌ SOME TESTS FAILED!")
            print("Please review the failed tests above.")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Error during compliance test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    import os
    
    if not os.path.exists('Statement.htm'):
        print("❌ Statement.htm file not found")
        print("Please ensure the Statement.htm file is in the current directory.")
        sys.exit(1)
    
    success = test_legend_format_compliance()
    sys.exit(0 if success else 1)
