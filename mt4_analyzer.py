import sys
import pandas as pd
import re
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QFileDialog, QLabel, QLineEdit, QSpinBox,
    QScrollArea, QMessageBox, QRadioButton, QButtonGroup
)
from PyQt5.QtCore import Qt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt

# --- Matplotlib Canvas Widget for PyQt5 Integration ---
# This class creates a Matplotlib figure and embeds it as a PyQt5 widget.
# This is the standard way to integrate Matplotlib with PyQt5.
class MplCanvas(FigureCanvas):
    """A custom Matplotlib canvas widget to integrate with PyQt5."""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        # Create a new Matplotlib figure with a tight layout
        fig = Figure(figsize=(width, height), dpi=dpi, tight_layout=True)
        self.axes = fig.add_subplot(111)
        super(MplCanvas, self).__init__(fig)
        self.setParent(parent)

# --- Main Application Window ---
class App(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('MT4 Statement Analyzer')
        self.setGeometry(100, 100, 1200, 800)
        
        # Central widget and main layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # --- UI Controls ---
        self.init_ui()

        # --- Member variables ---
        self.file_path = None

    def init_ui(self):
        """Initializes the User Interface components."""
        
        # --- Top Control Panel ---
        controls_layout = QHBoxLayout()

        # File selection button
        self.btn_load = QPushButton('Load MT4 Statement')
        self.btn_load.clicked.connect(self.open_file_dialog)
        controls_layout.addWidget(self.btn_load)

        # Read-only line edit to show the selected file path
        self.le_file_path = QLineEdit()
        self.le_file_path.setReadOnly(True)
        self.le_file_path.setPlaceholderText('No file selected...')
        controls_layout.addWidget(self.le_file_path)

        # EA Identification method selection
        self.id_method_label = QLabel("EA Identification:")
        controls_layout.addWidget(self.id_method_label)

        # Radio buttons for identification method
        self.radio_comment = QRadioButton("Comment")
        self.radio_magic = QRadioButton("Magic Number")
        self.radio_comment.setChecked(True)  # Default to comment method

        self.id_method_group = QButtonGroup()
        self.id_method_group.addButton(self.radio_comment)
        self.id_method_group.addButton(self.radio_magic)

        controls_layout.addWidget(self.radio_comment)
        controls_layout.addWidget(self.radio_magic)

        # Input for number of words in EA comment (only for comment method)
        self.word_count_label = QLabel("Words for EA ID:")
        controls_layout.addWidget(self.word_count_label)
        self.spin_word_count = QSpinBox()
        self.spin_word_count.setMinimum(1)
        self.spin_word_count.setValue(1) # Default to 1 word
        controls_layout.addWidget(self.spin_word_count)

        # Connect radio buttons to enable/disable word count
        self.radio_comment.toggled.connect(self.on_id_method_changed)
        self.radio_magic.toggled.connect(self.on_id_method_changed)

        # Analysis trigger button
        self.btn_analyze = QPushButton('Analyze')
        self.btn_analyze.clicked.connect(self.run_analysis)
        controls_layout.addWidget(self.btn_analyze)
        
        self.main_layout.addLayout(controls_layout)

        # --- Chart Display Area ---
        # A scroll area is used to accommodate multiple charts
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_widget = QWidget()
        self.charts_layout = QVBoxLayout(self.scroll_widget)
        self.scroll_area.setWidget(self.scroll_widget)
        
        self.main_layout.addWidget(self.scroll_area)

    def on_id_method_changed(self):
        """Enable/disable word count input based on identification method."""
        is_comment_method = self.radio_comment.isChecked()
        self.word_count_label.setEnabled(is_comment_method)
        self.spin_word_count.setEnabled(is_comment_method)

    def open_file_dialog(self):
        """Opens a file dialog to select an HTML file."""
        options = QFileDialog.Options()
        options |= QFileDialog.DontUseNativeDialog
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Select MT4 Statement File", "", "HTML Files (*.htm *.html)", options=options
        )
        if file_path:
            self.file_path = file_path
            self.le_file_path.setText(file_path)

    def parse_statement_html(self, file_path):
        """Parse the specific MT4 statement HTML format."""
        import re
        from bs4 import BeautifulSoup

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        soup = BeautifulSoup(content, 'html.parser')

        # Find all table rows
        rows = soup.find_all('tr')

        trades = []

        for i, row in enumerate(rows):
            cells = row.find_all('td')
            if not cells:
                continue

            # Check if this is a trade data row (has ticket, time, type, etc.)
            if len(cells) >= 14 and cells[2].get_text().strip() in ['buy', 'sell']:
                try:
                    # Extract basic trade data
                    ticket = cells[0].get_text().strip()
                    open_time = cells[1].get_text().strip()
                    trade_type = cells[2].get_text().strip()
                    size = cells[3].get_text().strip()
                    item = cells[4].get_text().strip()
                    price = cells[5].get_text().strip()
                    sl = cells[6].get_text().strip()
                    tp = cells[7].get_text().strip()
                    close_time = cells[8].get_text().strip()
                    close_price = cells[9].get_text().strip()
                    commission = cells[10].get_text().strip()
                    taxes = cells[11].get_text().strip()
                    swap = cells[12].get_text().strip()
                    profit = cells[13].get_text().strip()

                    # Extract magic number and comment from title attribute
                    title = cells[0].get('title', '')
                    magic_number = None
                    comment = None

                    if title:
                        # Title format: "#********* zscore-HOOL GBPUSD B0"
                        # Extract magic number (after #) and comment (rest)
                        if title.startswith('#'):
                            parts = title[1:].split(' ', 1)  # Remove # and split into max 2 parts
                            if len(parts) >= 1:
                                magic_number = parts[0]
                            if len(parts) >= 2:
                                comment = parts[1]

                    # Look for the next row which might contain additional magic/comment info
                    if i + 1 < len(rows):
                        next_row = rows[i + 1]
                        next_cells = next_row.find_all('td')

                        # Check if next row has magic number and comment in separate cells
                        if len(next_cells) >= 4:
                            # Look for magic number in the row (usually around index 9-10)
                            for j, cell in enumerate(next_cells):
                                cell_text = cell.get_text().strip()
                                # Magic numbers are typically numeric
                                if cell_text.isdigit() and len(cell_text) >= 6:
                                    if not magic_number:  # Only use if not already found in title
                                        magic_number = cell_text
                                    break

                            # Look for comment in the remaining cells
                            if len(next_cells) > 10:
                                comment_parts = []
                                for j in range(10, len(next_cells)):
                                    cell_text = next_cells[j].get_text().strip()
                                    if cell_text and cell_text != '&nbsp;':
                                        comment_parts.append(cell_text)
                                if comment_parts and not comment:  # Only use if not already found in title
                                    comment = ' '.join(comment_parts)

                    trade = {
                        'Ticket': ticket,
                        'Open Time': open_time,
                        'Type': trade_type,
                        'Size': size,
                        'Item': item,
                        'Price': price,
                        'S/L': sl,
                        'T/P': tp,
                        'Close Time': close_time,
                        'Close Price': close_price,
                        'Commission': commission,
                        'Taxes': taxes,
                        'Swap': swap,
                        'Profit': profit,
                        'Magic Number': magic_number or '',
                        'Comment': comment or ''
                    }

                    trades.append(trade)

                except (IndexError, AttributeError) as e:
                    continue

        return pd.DataFrame(trades)

    def run_analysis(self):
        """Main function to parse, process, and visualize the data."""
        if not self.file_path:
            QMessageBox.warning(self, "Warning", "Please select an MT4 statement file first.")
            return

        # Clear previous charts before starting a new analysis
        for i in reversed(range(self.charts_layout.count())):
            widget = self.charts_layout.itemAt(i).widget()
            if widget is not None:
                widget.deleteLater()

        try:
            # Determine identification method
            use_magic_number = self.radio_magic.isChecked()
            word_count = self.spin_word_count.value()

            # --- 1. HTML Parsing and Data Structuring ---
            df = self.parse_statement_html(self.file_path)

            if df.empty:
                raise ValueError("No trading data found in the statement file.")

            # --- 2. Data Cleaning and Type Conversion ---
            # Convert financial columns to numeric, coercing errors to NaN
            for col in ['Profit', 'Commission', 'Swap', 'Size']:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # Convert date columns to datetime objects
            df['Open Time'] = pd.to_datetime(df['Open Time'], errors='coerce')
            df['Close Time'] = pd.to_datetime(df['Close Time'], errors='coerce')

            # Drop rows where essential data could not be parsed
            df.dropna(subset=['Profit', 'Commission', 'Swap', 'Close Time'], inplace=True)

            # Calculate Net Profit for each trade
            df['Net Profit'] = df['Profit'] + df['Commission'] + df['Swap']

            # --- 3. EA Identification and Aggregation ---
            if use_magic_number:
                # Use Magic Number for identification
                df['EA_Name'] = df['Magic Number'].apply(
                    lambda x: f"Magic #{str(x)}" if pd.notna(x) and str(x).strip() else "No Magic Number"
                )
                id_method_text = "Magic Number"
            else:
                # Use Comment for identification
                df['EA_Name'] = df['Comment'].apply(
                    lambda x: ' '.join(str(x).split()[:word_count]) if pd.notna(x) else "No Comment"
                )
                id_method_text = f"Comment ({word_count} word{'s' if word_count > 1 else ''})"

            # Sort trades by closing time to ensure correct cumulative calculation
            df.sort_values(by='Close Time', inplace=True)

            # Group data by the identified EA name
            grouped_by_ea = df.groupby('EA_Name')

            if not len(grouped_by_ea):
                QMessageBox.information(self, "Info", "No identifiable EAs found based on the criteria.")
                return

            # Use a colormap to get distinct colors for each chart
            colors = plt.get_cmap('tab10').colors

            # --- 4. Chart Generation and Display ---
            for i, (ea_name, ea_df) in enumerate(grouped_by_ea):
                if ea_df.empty:
                    continue

                # Calculate cumulative profit and order count
                ea_df = ea_df.copy()  # Avoid SettingWithCopyWarning
                ea_df['Cumulative Profit'] = ea_df['Net Profit'].cumsum()
                ea_df['Order Count'] = range(1, len(ea_df) + 1)

                # Get start and end dates
                start_date = ea_df['Open Time'].min().strftime('%Y-%m-%d')
                end_date = ea_df['Close Time'].max().strftime('%Y-%m-%d')

                # Create a new chart canvas
                canvas = MplCanvas(self, width=8, height=4, dpi=100)

                # Plot the data
                ax = canvas.axes
                ax.plot(
                    ea_df['Order Count'],
                    ea_df['Cumulative Profit'],
                    label=f"EA: {ea_name}\nStart: {start_date}\nEnd: {end_date}\nTrades: {len(ea_df)}",
                    color=colors[i % len(colors)] # Cycle through colors
                )

                # Style the chart
                chart_title = f"Performance Analysis for: {ea_name}\n(Identified by {id_method_text})"
                ax.set_title(chart_title, fontsize=11)
                ax.set_xlabel("Cumulative Number of Orders")
                ax.set_ylabel("Cumulative Profit/Loss (Account Currency)")
                ax.legend(loc='upper left')
                ax.grid(True, linestyle='--', alpha=0.6)

                # Dynamically resize axes to fit data
                ax.autoscale_view()

                # Add the chart widget to the layout
                self.charts_layout.addWidget(canvas)

        except FileNotFoundError:
            QMessageBox.critical(self, "Error", f"File not found: {self.file_path}")
        except ValueError as ve:
            QMessageBox.critical(self, "Data Error", f"Could not process the file. It may be malformed or not a standard MT4 statement.\n\nDetails: {ve}")
        except Exception as e:
            QMessageBox.critical(self, "An Unexpected Error Occurred", f"An error occurred during analysis: {e}")

# --- Application Execution ---
if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = App()
    ex.show()
    sys.exit(app.exec_())
