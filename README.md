# MT4 Statement Analyzer

A comprehensive Python application for analyzing MetaTrader 4 (MT4) trading statements. This tool parses HTML MT4 statements, identifies Expert Advisors (EAs) based on comment fields, and generates performance charts for each EA.

## Features

- **GUI Interface**: User-friendly PyQt5 interface with file selection and analysis controls
- **HTML Parsing**: Automatically parses MT4 HTML statement files with robust parsing logic
- **Dual EA Identification Methods**:
  - **Comment-based**: Identifies Expert Advisors based on configurable comment field parsing (first N words)
  - **Magic Number-based**: Groups trades by their Magic Number values for precise EA identification
- **Performance Visualization**: Generates cumulative profit/loss charts for each identified EA
- **Scrollable Display**: Accommodates multiple charts in a scrollable interface
- **Data Validation**: Robust error handling and data validation
- **Export Ready**: Can be packaged into standalone executable
- **Real MT4 Compatibility**: Tested with actual MT4 statement files

## Requirements

- Python 3.7 or higher
- PyQt5
- pandas
- matplotlib
- lxml
- html5lib
- beautifulsoup4

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
python3 -m venv venv

source venv/bin/activate 

pip install -r requirements.txt
```

## Usage

1. Run the application:

```bash
python mt4_analyzer.py
```

2. Click "Load MT4 Statement" to select your HTML MT4 statement file
3. Choose EA identification method:
   - **Comment**: Uses the first N words from trade comments (configurable)
   - **Magic Number**: Uses the Magic Number field to group trades by EA
4. If using Comment method, set the number of words to use for EA identification (default: 1)
5. Click "Analyze" to process the file and generate charts

## How It Works

### EA Identification Methods

The application provides two methods for identifying Expert Advisors:

#### 1. Comment-Based Identification
Identifies EAs by parsing the "Comment" field in trade records. You can specify how many words from the beginning of the comment to use as the EA identifier.

For example:
- Comment: "MyEA v1.0 trade signal"
- With 1 word: EA = "MyEA"
- With 2 words: EA = "MyEA v1.0"

#### 2. Magic Number-Based Identification
Groups trades by their Magic Number values, which is often more reliable for EA identification since each EA typically uses a unique magic number.

For example:
- Magic Number: "202507211" → EA = "Magic #202507211"
- Magic Number: "202507112" → EA = "Magic #202507112"

**Recommendation**: Use Magic Number identification when available, as it provides more accurate EA grouping.

### Chart Generation
For each identified EA, the application generates a performance chart showing:
- **X-axis**: Cumulative number of orders
- **Y-axis**: Cumulative profit/loss (including commission and swap)
- **Enhanced Legend**: Detailed information including:
  - EA name (from selected identification method)
  - Currency pairs traded (comma-separated, alphabetically sorted)
  - Start and end dates (YYYY-MM-DD format)
  - Total number of trades
  - Format: `EA: [Name] | Pairs: [PAIR1, PAIR2] | Start: [DATE] | End: [DATE] | Trades: [COUNT]`
- **Grid lines**: Dotted grid for better readability
- **Visual styling**: Professional legend with white background, gray border, and shadow

### Data Processing
The application:
1. Parses HTML tables using pandas.read_html()
2. Filters for actual trading records (buy/sell orders)
3. Converts financial data to numeric format
4. Handles date/time conversion
5. Calculates net profit including commission and swap
6. Groups trades by identified EA
7. Generates cumulative performance metrics

## File Structure

```
MT4_Analyzer/
├── mt4_analyzer.py      # Main application file
├── requirements.txt     # Python dependencies
├── README.md           # This file
└── prjg25p.md         # Original project specification
```

## Creating a Standalone Executable

To create a standalone executable that doesn't require Python installation:

1. Install PyInstaller:
```bash
pip install pyinstaller
```

2. Create the executable:
```bash
pyinstaller --onefile --windowed --name MT4Analyzer mt4_analyzer.py
```

3. Find the executable in the `dist` folder

## Error Handling

The application includes comprehensive error handling for:
- File not found errors
- Malformed HTML files
- Missing or invalid data
- Data type conversion errors
- Empty datasets

## Supported MT4 Statement Formats

The application is designed to work with standard MT4 HTML statement exports. It expects:
- HTML format (.htm or .html files)
- Standard MT4 table structure
- Closed transactions table (typically the second table in the HTML)
- Standard column headers (Order, Time, Type, Size, etc.)

## Troubleshooting

### Common Issues

1. **"Could not find the required data table"**
   - Ensure the file is a valid MT4 HTML statement
   - Check that the statement contains closed transactions

2. **"No 'buy' or 'sell' trades found"**
   - Verify the statement contains actual trading records
   - Check that trades are marked as 'buy' or 'sell' in the Type column

3. **Charts not displaying**
   - Ensure all required dependencies are installed
   - Check that the EA identification found valid EAs

### Dependencies Issues

If you encounter issues with PyQt5 installation:
- On Ubuntu/Debian: `sudo apt-get install python3-pyqt5`
- On Windows: Use the pip installation as shown above
- On macOS: `brew install pyqt5`

## License

This project is provided as-is for educational and analysis purposes.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.
