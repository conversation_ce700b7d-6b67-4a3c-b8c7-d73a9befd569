#!/usr/bin/env python3
"""
Test script to verify the GUI functionality works with Statement.htm
This simulates the GUI analysis without actually opening the GUI.
"""

import sys
import os
sys.path.append('.')

# Import the App class from mt4_analyzer
from mt4_analyzer import App
from PyQt5.QtWidgets import QApplication

def test_gui_analysis():
    """Test the analysis functionality of the GUI application."""
    print("Testing GUI Analysis Functionality")
    print("=" * 40)
    
    # Create QApplication (required for Qt widgets)
    app = QApplication(sys.argv)
    
    try:
        # Create the main application window
        main_app = App()
        
        # Set the file path to Statement.htm
        main_app.file_path = 'Statement.htm'
        
        print("✓ Application created successfully")
        
        # Test Comment-based identification
        print("\nTesting Comment-based identification...")
        main_app.radio_comment.setChecked(True)
        main_app.spin_word_count.setValue(1)
        
        # Simulate running analysis (but don't actually show charts)
        try:
            # Parse the data
            df = main_app.parse_statement_html(main_app.file_path)
            print(f"✓ Parsed {len(df)} trades")
            
            # Test EA identification by comment
            word_count = main_app.spin_word_count.value()
            df['EA_Name'] = df['Comment'].apply(
                lambda x: ' '.join(str(x).split()[:word_count]) if pd.notna(x) else "No Comment"
            )
            
            comment_groups = df.groupby('EA_Name').size()
            print("  Comment-based EAs found:")
            for ea_name, count in comment_groups.items():
                print(f"    {ea_name}: {count} trades")
                
        except Exception as e:
            print(f"✗ Comment-based analysis failed: {e}")
            return False
        
        # Test Magic Number-based identification
        print("\nTesting Magic Number-based identification...")
        main_app.radio_magic.setChecked(True)
        
        try:
            # Test EA identification by magic number
            df['EA_Name'] = df['Magic Number'].apply(
                lambda x: f"Magic #{str(x)}" if pd.notna(x) and str(x).strip() else "No Magic Number"
            )
            
            magic_groups = df.groupby('EA_Name').size()
            print("  Magic Number-based EAs found:")
            for ea_name, count in magic_groups.items():
                print(f"    {ea_name}: {count} trades")
                
        except Exception as e:
            print(f"✗ Magic Number-based analysis failed: {e}")
            return False
        
        # Test data processing
        print("\nTesting data processing...")
        try:
            # Convert financial columns to numeric
            for col in ['Profit', 'Commission', 'Swap', 'Size']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Convert date columns
            df['Open Time'] = pd.to_datetime(df['Open Time'], errors='coerce')
            df['Close Time'] = pd.to_datetime(df['Close Time'], errors='coerce')
            
            # Calculate net profit
            df['Net Profit'] = df['Profit'] + df['Commission'] + df['Swap']
            
            total_profit = df['Net Profit'].sum()
            print(f"✓ Data processing successful")
            print(f"  Total Net Profit: {total_profit:.2f}")
            
        except Exception as e:
            print(f"✗ Data processing failed: {e}")
            return False
        
        # Test UI state management
        print("\nTesting UI state management...")
        try:
            # Test radio button functionality
            main_app.radio_comment.setChecked(True)
            main_app.on_id_method_changed()
            
            if main_app.word_count_label.isEnabled() and main_app.spin_word_count.isEnabled():
                print("✓ Comment mode enables word count controls")
            else:
                print("✗ Comment mode should enable word count controls")
                return False
            
            main_app.radio_magic.setChecked(True)
            main_app.on_id_method_changed()
            
            if not main_app.word_count_label.isEnabled() and not main_app.spin_word_count.isEnabled():
                print("✓ Magic Number mode disables word count controls")
            else:
                print("✗ Magic Number mode should disable word count controls")
                return False
                
        except Exception as e:
            print(f"✗ UI state management failed: {e}")
            return False
        
        print("\n✅ All GUI functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"✗ Failed to create application: {e}")
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    # Import pandas here to avoid issues
    import pandas as pd
    
    if not os.path.exists('Statement.htm'):
        print("✗ Statement.htm file not found")
        sys.exit(1)
    
    success = test_gui_analysis()
    sys.exit(0 if success else 1)
