{"devDependencies": {"eslint": "^6.8.0", "eslint-config-prettier": "^6.10.1", "prettier": "^2.0.2"}, "scripts": {"eslint": "eslint . --fix", "eslint:check": "eslint .", "lint": "npm run prettier && npm run eslint", "lint:check": "npm run prettier:check && npm run eslint:check", "prettier": "prettier --write \"**/*{.ts,.tsx,.js,.jsx,.css,.json}\"", "prettier:check": "prettier --check \"**/*{.ts,.tsx,.js,.jsx,.css,.json}\""}, "dependencies": {"@jsxtools/resize-observer": "^1.0.4"}}