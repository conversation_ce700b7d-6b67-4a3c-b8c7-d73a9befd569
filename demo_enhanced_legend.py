#!/usr/bin/env python3
"""
Demonstration script showing the enhanced legend functionality
with detailed EA information including currency pairs.
"""

import sys
import pandas as pd
from PyQt5.QtWidgets import QApplication
from mt4_analyzer import App

def demo_enhanced_legend():
    """Demonstrate the enhanced legend functionality."""
    print("MT4 Statement Analyzer - Enhanced Legend Demonstration")
    print("=" * 70)
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    try:
        # Create the analyzer
        analyzer = App()
        
        print("🎯 TESTING WITH REAL MT4 STATEMENT (Statement.htm)")
        print("=" * 70)
        
        analyzer.file_path = 'Statement.htm'
        df = analyzer.parse_statement_html(analyzer.file_path)
        
        if not df.empty:
            # Data processing
            for col in ['Profit', 'Commission', 'Swap', 'Size']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df['Open Time'] = pd.to_datetime(df['Open Time'], errors='coerce')
            df['Close Time'] = pd.to_datetime(df['Close Time'], errors='coerce')
            df['Net Profit'] = df['Profit'] + df['Commission'] + df['Swap']
            
            print(f"📊 Loaded {len(df)} trades from Statement.htm")
            
            # Test both identification methods
            methods = [
                ("Comment-based (1 word)", lambda x: ' '.join(str(x).split()[:1]) if pd.notna(x) else "No Comment"),
                ("Magic Number-based", lambda x: f"Magic #{str(x)}" if pd.notna(x) and str(x).strip() else "No Magic Number")
            ]
            
            for method_name, id_func in methods:
                print(f"\n📋 {method_name} Identification:")
                print("-" * 50)
                
                df['EA_Name'] = df['Comment'].apply(id_func) if "Comment" in method_name else df['Magic Number'].apply(id_func)
                grouped_by_ea = df.groupby('EA_Name')
                
                for ea_name, ea_df in grouped_by_ea:
                    if ea_df.empty:
                        continue
                    
                    # Calculate enhanced legend components
                    start_date = ea_df['Open Time'].min().strftime('%Y-%m-%d')
                    end_date = ea_df['Close Time'].max().strftime('%Y-%m-%d')
                    currency_pairs = ea_df['Item'].str.upper().unique()
                    currency_pairs = sorted([pair for pair in currency_pairs if pd.notna(pair) and pair.strip()])
                    pairs_text = ', '.join(currency_pairs) if currency_pairs else 'N/A'
                    
                    # Create the enhanced legend text (exactly as used in the app)
                    legend_text = f"EA: {ea_name} | Pairs: {pairs_text} | Start: {start_date} | End: {end_date} | Trades: {len(ea_df)}"
                    
                    print(f"\n🤖 Enhanced Legend:")
                    print(f"   {legend_text}")
                    print(f"   💰 Net Profit: {ea_df['Net Profit'].sum():.2f} USD")
                    print(f"   🎯 Win Rate: {(ea_df['Net Profit'] > 0).sum() / len(ea_df) * 100:.1f}%")
        
        print(f"\n{'='*70}")
        print("🎨 LEGEND FORMAT SPECIFICATION COMPLIANCE")
        print("=" * 70)
        
        print("✅ Required Format:")
        print("   'EA: [EA_Name] | Pairs: [PAIR1, PAIR2, PAIR3] | Start: [DATE] | End: [DATE] | Trades: [COUNT]'")
        print()
        
        print("✅ Implementation Features:")
        print("   • EA Name: Displays the identified EA name from selected method")
        print("   • Currency Pairs: Extracted from 'Item' column, sorted alphabetically")
        print("   • Date Range: Start and end dates in YYYY-MM-DD format")
        print("   • Trade Count: Total number of trades for the EA")
        print("   • Separator: Uses ' | ' for clear visual separation")
        print("   • Multiple Pairs: Comma-separated list when EA trades multiple pairs")
        print()
        
        print("✅ Visual Enhancements:")
        print("   • Legend positioned at upper left of each chart")
        print("   • Font size: 9pt for readability")
        print("   • Frame: White background with gray border")
        print("   • Shadow: Subtle shadow effect for better visibility")
        print("   • Transparency: 90% opacity (framealpha=0.9)")
        
        print(f"\n{'='*70}")
        print("🚀 USAGE INSTRUCTIONS")
        print("=" * 70)
        
        print("To see the enhanced legends in action:")
        print("1. Run the application: venv/bin/python mt4_analyzer.py")
        print("2. Load your MT4 statement file (Statement.htm)")
        print("3. Choose identification method:")
        print("   • Comment: For human-readable EA names")
        print("   • Magic Number: For precise EA identification")
        print("4. Click 'Analyze' to generate charts with enhanced legends")
        print()
        print("Each chart will display a detailed legend showing:")
        print("• The EA name as identified by your chosen method")
        print("• All currency pairs traded by that EA")
        print("• The date range of the EA's trading activity")
        print("• The total number of trades executed")
        
        print(f"\n✅ Enhanced legend demonstration completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during enhanced legend demonstration: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        app.quit()

if __name__ == "__main__":
    import os
    
    if not os.path.exists('Statement.htm'):
        print("❌ Statement.htm file not found")
        print("Please ensure the Statement.htm file is in the current directory.")
        sys.exit(1)
    
    success = demo_enhanced_legend()
    sys.exit(0 if success else 1)
