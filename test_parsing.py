#!/usr/bin/env python3
"""
Test script to verify the parsing logic works with the Statement.htm file.
"""

import sys
import pandas as pd
import re
from bs4 import BeautifulSoup

def parse_statement_html(file_path):
    """Parse the specific MT4 statement HTML format."""
    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()

    soup = BeautifulSoup(content, 'html.parser')

    # Find all table rows
    rows = soup.find_all('tr')

    trades = []

    for i, row in enumerate(rows):
        cells = row.find_all('td')
        if not cells:
            continue

        # Check if this is a trade data row (has ticket, time, type, etc.)
        if len(cells) >= 14 and cells[2].get_text().strip() in ['buy', 'sell']:
            try:
                # Extract basic trade data
                ticket = cells[0].get_text().strip()
                open_time = cells[1].get_text().strip()
                trade_type = cells[2].get_text().strip()
                size = cells[3].get_text().strip()
                item = cells[4].get_text().strip()
                price = cells[5].get_text().strip()
                sl = cells[6].get_text().strip()
                tp = cells[7].get_text().strip()
                close_time = cells[8].get_text().strip()
                close_price = cells[9].get_text().strip()
                commission = cells[10].get_text().strip()
                taxes = cells[11].get_text().strip()
                swap = cells[12].get_text().strip()
                profit = cells[13].get_text().strip()

                # Extract magic number and comment from title attribute
                title = cells[0].get('title', '')
                magic_number = None
                comment = None

                if title:
                    # Title format: "#********* zscore-HOOL GBPUSD B0"
                    # Extract magic number (after #) and comment (rest)
                    if title.startswith('#'):
                        parts = title[1:].split(' ', 1)  # Remove # and split into max 2 parts
                        if len(parts) >= 1:
                            magic_number = parts[0]
                        if len(parts) >= 2:
                            comment = parts[1]

                # Look for the next row which might contain additional magic/comment info
                if i + 1 < len(rows):
                    next_row = rows[i + 1]
                    next_cells = next_row.find_all('td')

                    # Check if next row has magic number and comment in separate cells
                    if len(next_cells) >= 4:
                        # Look for magic number in the row (usually around index 9-10)
                        for j, cell in enumerate(next_cells):
                            cell_text = cell.get_text().strip()
                            # Magic numbers are typically numeric
                            if cell_text.isdigit() and len(cell_text) >= 6:
                                if not magic_number:  # Only use if not already found in title
                                    magic_number = cell_text
                                break

                        # Look for comment in the remaining cells
                        if len(next_cells) > 10:
                            comment_parts = []
                            for j in range(10, len(next_cells)):
                                cell_text = next_cells[j].get_text().strip()
                                if cell_text and cell_text != '&nbsp;':
                                    comment_parts.append(cell_text)
                            if comment_parts and not comment:  # Only use if not already found in title
                                comment = ' '.join(comment_parts)

                trade = {
                    'Ticket': ticket,
                    'Open Time': open_time,
                    'Type': trade_type,
                    'Size': size,
                    'Item': item,
                    'Price': price,
                    'S/L': sl,
                    'T/P': tp,
                    'Close Time': close_time,
                    'Close Price': close_price,
                    'Commission': commission,
                    'Taxes': taxes,
                    'Swap': swap,
                    'Profit': profit,
                    'Magic Number': magic_number or '',
                    'Comment': comment or ''
                }

                trades.append(trade)

            except (IndexError, AttributeError) as e:
                continue

    return pd.DataFrame(trades)

def test_parsing():
    """Test the parsing with Statement.htm file."""
    print("Testing MT4 Statement Parsing")
    print("=" * 40)
    
    try:
        # Parse the statement
        df = parse_statement_html('Statement.htm')
        
        print(f"✓ Successfully parsed {len(df)} trades")
        print(f"✓ Columns: {list(df.columns)}")
        
        if not df.empty:
            print("\nFirst few trades:")
            print("-" * 40)
            for i, row in df.head(3).iterrows():
                print(f"Trade {i+1}:")
                print(f"  Ticket: {row['Ticket']}")
                print(f"  Type: {row['Type']}")
                print(f"  Item: {row['Item']}")
                print(f"  Profit: {row['Profit']}")
                print(f"  Magic Number: {row['Magic Number']}")
                print(f"  Comment: {row['Comment']}")
                print()
            
            # Test EA identification by comment
            print("EA Identification by Comment (1 word):")
            print("-" * 40)
            df['EA_Name_Comment'] = df['Comment'].apply(
                lambda x: ' '.join(str(x).split()[:1]) if pd.notna(x) else "No Comment"
            )
            comment_groups = df.groupby('EA_Name_Comment').size()
            for ea_name, count in comment_groups.items():
                print(f"  {ea_name}: {count} trades")
            
            print("\nEA Identification by Magic Number:")
            print("-" * 40)
            df['EA_Name_Magic'] = df['Magic Number'].apply(
                lambda x: f"Magic #{str(x)}" if pd.notna(x) and str(x).strip() else "No Magic Number"
            )
            magic_groups = df.groupby('EA_Name_Magic').size()
            for ea_name, count in magic_groups.items():
                print(f"  {ea_name}: {count} trades")
            
            # Test data conversion
            print("\nData Conversion Test:")
            print("-" * 40)
            for col in ['Profit', 'Commission', 'Swap', 'Size']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                valid_count = df[col].notna().sum()
                print(f"  {col}: {valid_count}/{len(df)} valid numeric values")
            
            # Test date conversion
            df['Open Time'] = pd.to_datetime(df['Open Time'], errors='coerce')
            df['Close Time'] = pd.to_datetime(df['Close Time'], errors='coerce')
            
            open_time_valid = df['Open Time'].notna().sum()
            close_time_valid = df['Close Time'].notna().sum()
            print(f"  Open Time: {open_time_valid}/{len(df)} valid dates")
            print(f"  Close Time: {close_time_valid}/{len(df)} valid dates")
            
            # Calculate net profit
            df['Net Profit'] = df['Profit'] + df['Commission'] + df['Swap']
            total_profit = df['Net Profit'].sum()
            print(f"\nTotal Net Profit: {total_profit:.2f}")
            
        else:
            print("⚠️  No trades found in the statement")
            
    except FileNotFoundError:
        print("✗ Statement.htm file not found")
        return False
    except Exception as e:
        print(f"✗ Error during parsing: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = test_parsing()
    sys.exit(0 if success else 1)
